# CT-LIO 项目技术要点记录

## 项目概述
这是一个基于激光雷达和IMU的SLAM系统（CT-LIO），使用连续时间优化进行位姿估计。

## 核心函数分析

### 1. optimize() 函数 - 位姿优化
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:409`
**作用**: 使用Ceres优化器进行帧间位姿估计

**关键流程**:
1. 获取前一帧和当前帧状态信息
2. 对点云进行下采样: `gridSampling()`
3. 坐标变换: `transformKeypoints()` lambda函数将点云从LIDAR系转到世界系
4. 生成代价函数: `addSurfCostFactor()`
5. 配置Ceres优化器，使用LM算法求解非线性最小二乘问题

**ICP模型区别**:
- `POINT_TO_PLANE`: 只优化帧末位姿(`end_quat`, `end_t`)
- `CT_POINT_TO_PLANE`: 优化帧首尾完整位姿，考虑运动畸变

### 2. addSurfCostFactor() 函数 - 代价函数生成
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:981`
**作用**: 为每个点云关键点生成点到平面的代价函数

**工作流程**:
1. **邻域搜索**: 使用`searchNeighbors()`在体素地图中找最近邻
2. **平面拟合**: `estimatePointNeighborhood()`计算局部平面法向量
3. **权重计算**: 
   - `lambda_weight`: 平面性权重系数
   - `lambda_neighborhood`: 邻域距离权重系数
   - 最终权重 = `lambda_weight * planarity_weight + lambda_neighborhood * distance_weight`
4. **距离检查**: 过滤距离平面过远的点
5. **生成Ceres代价函数**: 每个有效点生成一个几何约束

#### 2.1 computeNeighborhoodDistribution() - 邻域几何特征计算
**作用**: 计算邻域点集的几何分布特征，提取平面信息

**算法步骤**:
1. **质心计算**: `barycenter = Σpoints / n` - 计算所有邻域点的平均位置
2. **协方差矩阵**: `C(i,j) = Σ[(point(i) - barycenter(i)) * (point(j) - barycenter(j))]`
3. **特征值分解**: 对协方差矩阵进行SVD分解获得特征值和特征向量
4. **法向量提取**: 最小特征值对应的特征向量作为平面法向量
5. **平面性指标**: `a2D = (σ₂ - σ₃) / σ₁`，值越接近1表示越像平面

#### 2.2 estimatePointNeighborhood() - 邻域估计
**作用**: 结合当前查询点坐标，完善邻域几何信息

**处理流程**:
1. 调用`computeNeighborhoodDistribution()`计算基础几何特征
2. 计算平面性权重: `planarity_weight = pow(a2D, power_planarity)`
3. 法向量方向修正: 确保法向量指向正确方向
4. 返回包含完整几何信息的`Neighborhood`结构体

### 3. searchNeighbors() 函数 - K最近邻搜索
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:1124`
**作用**: 在体素地图中高效搜索K个最近邻点

**算法实现**:
1. **体素索引计算**: `point[x] / size_voxel_map` 确定体素坐标
2. **邻域遍历**: 3层嵌套循环遍历`nb_voxels_visited`范围内的体素
3. **质量过滤**: 
   - 体素点数量过滤: `threshold_voxel_capacity`
   - 法向量相似性过滤: `dot_product > 0` (夹角<90°)
4. **K-NN维护**: 使用优先队列(最大堆)维护最近的K个点
5. **结果排序**: 返回按距离从近到远排序的邻居点

### 4. checkLocalizability() 函数 - 退化场景检测
**作用**: 通过分析法向量分布检测几何退化情况

**检测方法**:
1. **构建法向量矩阵**: 将所有参与优化的法向量组成N×3矩阵
2. **奇异值分解**: 对法向量矩阵进行SVD分解
3. **退化判断**: 当最小奇异值`σ₃ < 3.5`时报警
4. **几何意义**: σ₃过小表示法向量在某方向分布稀少，缺乏几何约束

**退化场景**:
- 长走廊: 缺乏横向约束
- 空旷场地: 缺乏垂直约束  
- 单调环境: 法向量方向单一

### 5. Ceres优化配置与执行
**优化器配置**:
```cpp
options.max_num_iterations = 5;        // 最大迭代5次，保证实时性
options.num_threads = 3;               // 3线程并行计算
options.trust_region_strategy_type = LEVENBERG_MARQUARDT;  // LM算法
```

**约束生成过程**:
1. **遍历关键点**: 对每个下采样后的关键点
2. **邻域搜索**: 在体素地图中找K近邻
3. **几何验证**: 计算点到平面距离，过滤异常匹配
4. **权重计算**: 结合平面性权重和距离权重
5. **添加约束**: 根据ICP模型添加到Ceres问题中

## 关键技术点

### 体素地图 (Voxel Map)
- 将3D空间划分为规则网格，实现O(1)的空间查询
- 每个体素存储局部点云和法向量信息
- 支持高效的邻域搜索和地图更新

### 坐标系处理
**所有计算均在WORLD坐标系进行**:
- **邻域搜索**: 在世界系体素地图中执行
- **几何计算**: 协方差矩阵和法向量都基于世界系坐标
- **距离计算**: 点到平面距离在世界系下计算
- **注意事项**: 传统POINT_TO_PLANE模式存在坐标系混用问题

### 平面特征 vs 非平面特征
**平面特征** (a2D ≈ 1):
- 墙面、地面、桌面等大平面结构
- 提供稳定的几何约束
- 对传感器噪声鲁棒

**非平面特征** (a2D ≈ 0):
- 线状特征: 电线杆、管道、建筑边缘
- 点状特征: 树木、噪声点
- 复杂几何: 球面、圆柱面

### 法向量匹配
- 通过法向量点积判断平面相似性: `normal1.dot(normal2) > 0`
- 避免不同朝向平面间的错误匹配
- 提高ICP收敛精度和鲁棒性

### 权重融合策略
**复合权重计算**:
```cpp
weight = lambda_weight * planarity_weight + lambda_neighborhood * exp(-distance/threshold)
```
**组成部分**:
- **平面性权重**: `pow(a2D, power_planarity)` - 几何质量评估
- **距离权重**: `exp(-distance/threshold)` - 空间一致性约束
- **参数平衡**: 确保既有几何可靠性又有空间合理性

### 平面性权重
```cpp
planarity_weight = std::pow(neighborhood.a2D, options_.power_planarity);
```
- `neighborhood.a2D`: 平面性指标(0-1)
- `power_planarity`越大，对平面特征选择性越强
- 通过幂函数增强平面特征明显点的权重
- 抑制噪声点对优化的影响

### 运动补偿策略
- **buildFrame()**: 初始运动补偿，基于IMU预测
- **optimize()**: 精确运动补偿，通过优化重新估计运动轨迹
- CT模型支持连续时间运动建模，处理高速运动场景

## 数据流概览
1. IMU预测 → 2. 点云去畸变 → 3. 体素地图匹配 → 4. Ceres优化 → 5. 位姿更新 → 6. 地图增量更新

## 优化配置
- **损失函数**: HuberLoss(0.5) - 鲁棒损失函数
- **优化算法**: Levenberg-Marquardt
- **最大迭代**: 5次
- **收敛阈值**: 平移和旋转变化量阈值

## 重要参数详解
- `surf_res`: 表面点分辨率 - 控制关键点下采样密度
- `size_voxel_map`: 体素地图分辨率 - 影响邻域搜索效率和精度
- `max_dist_to_plane_icp`: 点到平面最大距离(米) - ICP匹配的几何阈值
- `voxel_neighborhood`: 邻域搜索范围 - 体素搜索半径
- `power_planarity`: 平面性权重指数(通常2-4) - 控制平面特征选择严格程度
- `num_closest_neighbors`: K近邻数量 - 每个点的候选匹配数
- `lambda_weight`: 平面性权重系数 - 几何质量的重要性
- `lambda_neighborhood`: 邻域距离权重系数 - 空间一致性的重要性
- `threshold_voxel_capacity`: 体素点数量阈值 - 过滤稀疏体素
- `nb_voxels_visited`: 体素搜索范围 - 邻域搜索窗口大小

## 坐标系变换
- **TIL_**: LIDAR到IMU的变换矩阵
- **变换链**: LIDAR → IMU → World
- `keypoint.point = R * (TIL_ * keypoint.raw_point) + t`