# CT-LIO 项目技术要点记录

## 项目概述
这是一个基于激光雷达和IMU的SLAM系统（CT-LIO），使用连续时间优化进行位姿估计。

## 核心函数分析

### 1. optimize() 函数 - 位姿优化
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:409`
**作用**: 使用Ceres优化器进行帧间位姿估计

**关键流程**:
1. 获取前一帧和当前帧状态信息
2. 对点云进行下采样: `gridSampling()`
3. 坐标变换: `transformKeypoints()` lambda函数将点云从LIDAR系转到世界系
4. 生成代价函数: `addSurfCostFactor()`
5. 配置Ceres优化器，使用LM算法求解非线性最小二乘问题

**ICP模型区别**:
- `POINT_TO_PLANE`: 只优化帧末位姿(`end_quat`, `end_t`)
- `CT_POINT_TO_PLANE`: 优化帧首尾完整位姿，考虑运动畸变

### 2. addSurfCostFactor() 函数 - 代价函数生成
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:981`
**作用**: 为每个点云关键点生成点到平面的代价函数

**工作流程**:
1. **邻域搜索**: 使用`searchNeighbors()`在体素地图中找最近邻
2. **平面拟合**: `estimatePointNeighborhood()`计算局部平面法向量
3. **权重计算**: 
   - `lambda_weight`: 平面性权重系数
   - `lambda_neighborhood`: 邻域距离权重系数
   - 最终权重 = `lambda_weight * planarity_weight + lambda_neighborhood * distance_weight`
4. **距离检查**: 过滤距离平面过远的点
5. **生成Ceres代价函数**: 每个有效点生成一个几何约束

### 3. searchNeighbors() 函数 - K最近邻搜索
**位置**: `src/ct-lio/src/liw/lio/lidarodom.cpp:1124`
**作用**: 在体素地图中高效搜索K个最近邻点

**算法实现**:
1. **体素索引计算**: `point[x] / size_voxel_map` 确定体素坐标
2. **邻域遍历**: 3层嵌套循环遍历`nb_voxels_visited`范围内的体素
3. **质量过滤**: 
   - 体素点数量过滤: `threshold_voxel_capacity`
   - 法向量相似性过滤: `dot_product > 0` (夹角<90°)
4. **K-NN维护**: 使用优先队列(最大堆)维护最近的K个点
5. **结果排序**: 返回按距离从近到远排序的邻居点

## 关键技术点

### 体素地图 (Voxel Map)
- 将3D空间划分为规则网格，实现O(1)的空间查询
- 每个体素存储局部点云和法向量信息
- 支持高效的邻域搜索和地图更新

### 法向量匹配
- 通过法向量点积判断平面相似性: `normal1.dot(normal2) > 0`
- 避免不同朝向平面间的错误匹配
- 提高ICP收敛精度和鲁棒性

### 平面性权重
```cpp
planarity_weight = std::pow(neighborhood.a2D, options_.power_planarity);
```
- `neighborhood.a2D`: 平面性指标(0-1)
- 通过幂函数增强平面特征明显点的权重
- 抑制噪声点对优化的影响

### 运动补偿策略
- **buildFrame()**: 初始运动补偿，基于IMU预测
- **optimize()**: 精确运动补偿，通过优化重新估计运动轨迹
- CT模型支持连续时间运动建模，处理高速运动场景

## 数据流概览
1. IMU预测 → 2. 点云去畸变 → 3. 体素地图匹配 → 4. Ceres优化 → 5. 位姿更新 → 6. 地图增量更新

## 优化配置
- **损失函数**: HuberLoss(0.5) - 鲁棒损失函数
- **优化算法**: Levenberg-Marquardt
- **最大迭代**: 5次
- **收敛阈值**: 平移和旋转变化量阈值

## 重要参数
- `surf_res`: 表面点分辨率
- `size_voxel_map`: 体素地图分辨率  
- `max_dist_to_plane_icp`: 点到平面最大距离
- `voxel_neighborhood`: 邻域搜索范围
- `power_planarity`: 平面性权重指数

## 坐标系变换
- **TIL_**: LIDAR到IMU的变换矩阵
- **变换链**: LIDAR → IMU → World
- `keypoint.point = R * (TIL_ * keypoint.raw_point) + t`