YAML: 1.0

preprocess:
  point_filter_num: 1
  lidar_type: 5  # 1-AVIA 2-velodyne 3-ouster  4-robosense 5-pandar
  blind: 1.5
  use_points_with_valid : false #remove points timestamp <=0
  normal_est : true
  notimestamp : false #没有时间戳的傻逼kitti数据集

common:
  imu_topic: /lpms_imu/data #/imu/data #/imu_raw   /gps/gtimu /lpms_imu/data
  lid_topic: /hesai/pandar  #/points_raw  /rslidar_points /velodyne_points  /hesai/pandar  /nv_liom/normal_vector_cloud
  # lid_topic2: /ns2/velodyne_points #/points_raw  /rslidar_points /velodyne_points  /hesai/pandar  /nv_liom/normal_vector_cloud /livox/lidar

mapping:
  extrinsic_est_en: false
  # extrinsic_T: [ 0.0, 0.0, -0.05]
  # # extrinsic_T: [ -0.01378, 0.0558, -0.11003]
  # extrinsic_R: [ 1, 0, 0,  0, 1, 0,  0, 0, 1] #indoor xt32

  # extrinsic_T: [ 0.0, 0.0, -0.1]
  # extrinsic_R: [ 1, 0, 0,0, 0, 1,0, -1, 0]  # rata

  # extrinsic_T: [0.0, 0.0, -0.1]
  # extrinsic_R: [0, -1,0,1, 0, 0,0, 0,1] #hesaixl36000
  # extrinsic_R: [0, 1, 0,-1, 0, 0,0, 0, 1] #dbdx loop
  extrinsic_T: [ 0, 0, -0.05]
  extrinsic_R: [ -1,0, 0, 0, 1, 0,  0, 0,-1]
delay_time: 0.15

odometry:
  downsample: true
  surf_res: 0.1
  sampling_rate: 1.

  log_print: false
  need_lidar_frame: false #if u need lidar frame topic
  icp_with_normal: false #if u need normal information for icp
  max_num_iteration: 4
  # ct_icp
  icpmodel: POINT_TO_PLANE                    # Options: [CT_POINT_TO_PLANE, POINT_TO_PLANE]
  # size_voxel_map: 0.5                         # The voxel size of in the voxel map
  # min_distance_points: 0.065                   # The minimum distance between two points to be considered in the voxel map street 0.065 
  # max_num_points_in_voxel: 20                 # The maximum number of points per voxel of the map
  size_voxel_map: 0.5                         # The voxel size of in the voxel map
  min_distance_points: 0.065                   # The minimum distance between two points to be considered in the voxel map street 0.065 
  max_num_points_in_voxel: 20                # The maximum number of points per voxel of the map
  min_num_points: 0
  max_distance: 120                        # The threshold of the distance to suppress voxels from the map
  weight_alpha: 0.95
  weight_neighborhood: 0.05
  max_dist_to_plane_icp: 1
  init_num_frames: 20
  voxel_neighborhood: 1
  max_number_neighbors: 20
  threshold_voxel_occupancy: 1
  estimate_normal_from_neighborhood: true
  undistor: true # turn on when undistortion is not processed outside 
  pcd_icp: false # turn on when using pcd icp
  min_number_neighbors: 20                    # The minimum number of neighbor points to define a valid neighborhood
  power_planarity: 2.0
  num_closest_neighbors: 1
  
  sampling_rate: 1.
  ratio_of_nonground: 2
  max_num_residuals: 2000
  min_num_residuals: 300
  motion_compensation: CONSTANT_VELOCITY #NONE, CONSTANT_VELOCITY, ITERATIVE, CONTINUOUS
  beta_location_consistency: 1.0
  beta_orientation_consistency: 1.0
  beta_constant_velocity: 0.0
  beta_small_velocity: 0.0

  thres_translation_norm: 0.01            # m
  thres_orientation_norm: 0.1             # deg

  save_pcd : False  
  save_pcd_body : False
  pose_save_en : False